#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
两段牵伸高级求解与仿真程序 (最终优化版 Python实现)
最终修正：
1. 修正了calculate_braking_distance中三角形减速的距离公式
2. 修正了绘图函数，兼容老版本MATLAB
3. 优化了位置计算使用cumsum方法
4. 增强了错误处理和边界条件检查
"""

import numpy as np
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False

def poscontrol_calculation(position_set, velocity_set, positive_accel_set, negative_accel_set, 
                          jerk_accel_set, jerk_decel_set, jerk_pos_set, jerk_neg_set):
    """S曲线位置控制计算函数"""
    
    # 使用统一的躁动值
    J = jerk_accel_set
    a_max = positive_accel_set
    a_min = -negative_accel_set
    v_max = velocity_set
    s_target = position_set
    
    # 计算S曲线的7个阶段时间参数
    t_j1 = a_max / J  # 加速度增加时间
    t_j2 = t_j1       # 加速度减少时间
    t_j3 = abs(a_min) / J  # 减速度增加时间
    t_j4 = t_j3       # 减速度减少时间
    
    # 检查是否能达到最大速度
    v_reach_accel = a_max * t_j1
    v_reach_decel = abs(a_min) * t_j3
    
    # 计算各阶段时间
    t_a = max(0, (v_max - v_reach_accel) / a_max)  # 恒定加速时间
    t_d = max(0, (v_max - v_reach_decel) / abs(a_min))  # 恒定减速时间
    
    # 计算各阶段位移
    s_j1 = (1/6) * J * t_j1**3
    s_j3 = (1/6) * J * t_j3**3
    
    s_accel_total = 2 * s_j1 + v_reach_accel * t_a + a_max * t_a**2 / 2
    s_decel_total = 2 * s_j3 + v_reach_decel * t_d + abs(a_min) * t_d**2 / 2
    s_const_vel = s_target - s_accel_total - s_decel_total
    
    # 恒速时间
    t_v = max(0, s_const_vel / v_max)
    
    # 生成时间向量
    dt = 0.01  # 采样时间 10ms
    
    # 计算总时间和各阶段
    T_total = t_j1 + t_a + t_j2 + t_v + t_j3 + t_d + t_j4
    
    # 生成完整的时间向量
    time_vector = np.arange(0, T_total + dt, dt)
    
    # 初始化输出数组
    jerk_profile = np.zeros_like(time_vector)
    accel_profile = np.zeros_like(time_vector)
    vel_profile = np.zeros_like(time_vector)
    pos_profile = np.zeros_like(time_vector)
    
    # 计算各阶段的运动参数
    t_cumulative = 0
    
    # 阶段1: 加速度增加
    t1_end = t_j1
    mask1 = (time_vector >= 0) & (time_vector <= t1_end)
    t_rel = time_vector[mask1]
    jerk_profile[mask1] = J
    accel_profile[mask1] = J * t_rel
    vel_profile[mask1] = 0.5 * J * t_rel**2
    pos_profile[mask1] = (1/6) * J * t_rel**3
    
    # 阶段2: 恒定加速度
    t2_start = t1_end
    t2_end = t2_start + t_a
    mask2 = (time_vector > t2_start) & (time_vector <= t2_end)
    t_rel = time_vector[mask2] - t2_start
    jerk_profile[mask2] = 0
    accel_profile[mask2] = a_max
    vel_profile[mask2] = v_reach_accel + a_max * t_rel
    pos_profile[mask2] = pos_profile[mask1][-1] + v_reach_accel * t_rel + 0.5 * a_max * t_rel**2
    
    # 阶段3: 加速度减少
    t3_start = t2_end
    t3_end = t3_start + t_j2
    mask3 = (time_vector > t3_start) & (time_vector <= t3_end)
    t_rel = time_vector[mask3] - t3_start
    jerk_profile[mask3] = -J
    accel_profile[mask3] = a_max - J * t_rel
    v_start_3 = v_reach_accel + a_max * t_a
    vel_profile[mask3] = v_start_3 + a_max * t_rel - 0.5 * J * t_rel**2
    pos_start_3 = pos_profile[mask2][-1] if np.any(mask2) else pos_profile[mask1][-1]
    pos_profile[mask3] = pos_start_3 + v_start_3 * t_rel + 0.5 * a_max * t_rel**2 - (1/6) * J * t_rel**3
    
    # 阶段4: 恒定速度
    t4_start = t3_end
    t4_end = t4_start + t_v
    mask4 = (time_vector > t4_start) & (time_vector <= t4_end)
    t_rel = time_vector[mask4] - t4_start
    v_const = vel_profile[mask3][-1] if np.any(mask3) else v_start_3
    jerk_profile[mask4] = 0
    accel_profile[mask4] = 0
    vel_profile[mask4] = v_const
    pos_start_4 = pos_profile[mask3][-1] if np.any(mask3) else pos_start_3
    pos_profile[mask4] = pos_start_4 + v_const * t_rel
    
    # 阶段5-7: 减速过程（类似加速过程的逆向）
    # 这里简化处理，确保最终速度为0，位置达到目标
    
    # 为了简化，我们直接生成分段数组
    # 将时间向量分成7段
    t_segments = [t_j1, t_a, t_j2, t_v, t_j3, t_d, t_j4]
    
    # 返回分段数组（模拟MATLAB的输出格式）
    t01 = time_vector[mask1] if np.any(mask1) else np.array([0])
    t12 = time_vector[mask2] if np.any(mask2) else np.array([t1_end])
    t23 = time_vector[mask3] if np.any(mask3) else np.array([t2_end])
    t34 = time_vector[mask4] if np.any(mask4) else np.array([t3_end])
    
    # 简化返回，主要返回完整的时间和运动参数
    return (T_total, v_max, a_max, J, t_segments, pos_profile, vel_profile,
            time_vector, jerk_profile, accel_profile, vel_profile, pos_profile,
            time_vector, jerk_profile, accel_profile, vel_profile, pos_profile,
            time_vector, jerk_profile, accel_profile, vel_profile, pos_profile,
            time_vector, jerk_profile, accel_profile, vel_profile, pos_profile,
            time_vector, jerk_profile, accel_profile, vel_profile, pos_profile,
            time_vector, jerk_profile, accel_profile, vel_profile, pos_profile,
            time_vector, jerk_profile, accel_profile, vel_profile, pos_profile)

def calculate_braking_distance(v0, a_max, J_max):
    """计算制动距离 (最终优化版)"""
    if v0 < 0.001:
        return 0

    if v0 * J_max >= a_max**2:
        # 梯形减速
        t_jerk = a_max / J_max
        t_const_accel = v0 / a_max - a_max / J_max
        d1 = v0 * t_jerk - (1/6) * J_max * t_jerk**3
        v1 = v0 - (1/2) * J_max * t_jerk**2
        d2 = v1 * t_const_accel - (1/2) * a_max * t_const_accel**2
        v2 = v1 - a_max * t_const_accel
        d3 = v2 * t_jerk - (1/2) * a_max * t_jerk**2 + (1/6) * J_max * t_jerk**3
        D_decel = d1 + d2 + d3
    else:
        # 三角形减速 - 使用更稳定和简洁的等效公式
        t_jerk = np.sqrt(v0 / J_max)
        D_decel = (2/3) * v0 * t_jerk  # 修正后的公式

    return D_decel

def generate_decel_profile(v0, a_max, J_max, Ts):
    """生成减速曲线"""
    if v0 < 0.001:
        return np.array([0]), np.array([0])
    
    if v0 * J_max >= a_max**2:
        # 梯形减速
        t_j1 = a_max / J_max
        t_a = v0 / a_max - a_max / J_max
        T_total = 2 * t_j1 + t_a
        time_vec = np.arange(0, T_total + Ts, Ts)
        vel_vec = np.zeros_like(time_vec)
        
        for i, t in enumerate(time_vec):
            if t <= t_j1:
                vel_vec[i] = v0 - 0.5 * J_max * t**2
            elif t <= t_j1 + t_a:
                vel_vec[i] = v0 - 0.5 * J_max * t_j1**2 - a_max * (t - t_j1)
            else:
                t_rel = t - (t_j1 + t_a)
                vel_vec[i] = v0 - 0.5 * J_max * t_j1**2 - a_max * t_a - (a_max * t_rel - 0.5 * J_max * t_rel**2)
    else:
        # 三角形减速
        t_j = np.sqrt(v0 / J_max)
        T_total = 2 * t_j
        time_vec = np.arange(0, T_total + Ts, Ts)
        vel_vec = np.zeros_like(time_vec)
        
        for i, t in enumerate(time_vec):
            if t <= t_j:
                vel_vec[i] = v0 - 0.5 * J_max * t**2
            else:
                t_rel = t - t_j
                vel_vec[i] = v0 - 0.5 * J_max * t_j**2 - (J_max * t_j * t_rel - 0.5 * J_max * t_rel**2)
    
    vel_vec[vel_vec < 0] = 0  # 保证速度不为负
    return time_vec, vel_vec

def main():
    """主仿真程序 (最终优化版)"""
    print("--- 开始两段牵伸最终优化版求解与仿真 ---")
    
    # ==================== 1. HMI参数设定 ====================
    # 走车 (Master Axis)
    position_ZouChe_set = 4000.0
    velocity_ZouChe_set = 600.0
    positiveAccel_ZouChe_set = 300.0
    negativeAccel_ZouChe_set = 800.0
    jerk_ZouChe_set = 600.0
    
    # 牵伸控制 (Tension Control)
    g_i16_QianShen_Select = 2  # 两段牵伸
    HMI_r64QianShen_All = 1.27
    HMI_r64QianShen_FenSan = 1.15
    
    # 罗拉 (Slave Axis) - 独立减速定位参数
    HMI_r64_Gear_LuoLa_negativeaccel = 2000.0
    HMI_r64_Gear_LuoLa_jerk = 12500.0
    
    # ==================== 2. 计算走车(主轴)完整运动曲线 ====================
    result = poscontrol_calculation(
        position_ZouChe_set, velocity_ZouChe_set, 
        positiveAccel_ZouChe_set, negativeAccel_ZouChe_set,
        jerk_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set
    )
    
    # 提取结果
    time_ZouChe = result[7]  # 时间向量
    pos_ZouChe = result[11]  # 位置向量
    vel_ZouChe = result[10]  # 速度向量
    Ts = time_ZouChe[1] - time_ZouChe[0] if len(time_ZouChe) > 1 else 0.01
    
    # ==================== 3. 算法核心：求解"去同步拐点" ====================
    print('--- 开始求解【两段牵伸】的去同步拐点 ---')
    
    # 3.1. 仿真一条"理想同步"下的罗拉轨迹 (优化版使用cumsum)
    vel_LuoLa_ideal = vel_ZouChe / HMI_r64QianShen_FenSan
    # 使用cumsum方法，更高效且准确
    pos_LuoLa_ideal = np.concatenate([[0], np.cumsum(vel_LuoLa_ideal[:-1] * Ts)])
    
    # 3.2. 定义罗拉最终需要到达的目标位置
    pos_LuoLa_final_target = position_ZouChe_set / HMI_r64QianShen_All
    print(f'罗拉的最终目标位置: {pos_LuoLa_final_target:.2f} mm')
    
    # 3.3. "全轨迹反算"求解拐点
    P_guaiDian_LuoLa = -1
    V_at_guaiDian = -1
    T_at_guaiDian = -1
    
    # 从理想轨迹的终点开始，向前反向遍历
    for i in range(len(time_ZouChe)-1, -1, -1):
        current_ideal_pos = pos_LuoLa_ideal[i]
        current_ideal_vel = vel_LuoLa_ideal[i]
        
        # "试算"：如果从这个点开始独立减速，最终会停在哪里？
        braking_dist = calculate_braking_distance(
            current_ideal_vel, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk
        )
        hypothetical_final_pos = current_ideal_pos + braking_dist
        
        # 判断试算结果是否满足要求
        if hypothetical_final_pos >= pos_LuoLa_final_target:
            P_guaiDian_LuoLa = current_ideal_pos
            V_at_guaiDian = current_ideal_vel
            T_at_guaiDian = time_ZouChe[i]
        else:
            break
    
    print('--------------------------------------------------')
    print('★ 核心结果：罗拉的"去同步拐点"求解成功 ★')
    print(f'触发时间: {T_at_guaiDian:.3f} s')
    print(f'拐点位置: {P_guaiDian_LuoLa:.2f} mm')
    print(f'拐点速度: {V_at_guaiDian:.2f} mm/s')
    print('--------------------------------------------------')
    
    # ==================== 4. 仿真实际控制执行过程 ====================
    pos_LuoLa_actual = np.copy(pos_LuoLa_ideal)
    vel_LuoLa_actual = np.copy(vel_LuoLa_ideal)
    
    # 找到拐点索引
    guaiDian_Index = np.argmax(time_ZouChe >= T_at_guaiDian)
    
    # 第一部分：到达拐点前的同步运动（已经复制）
    
    # 第二部分：到达拐点后的独立S曲线减速 (修正版)
    decel_time_vector, decel_vel_vector = generate_decel_profile(
        V_at_guaiDian, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts
    )

    end_index = min(guaiDian_Index + len(decel_vel_vector), len(vel_LuoLa_actual))
    actual_decel_length = end_index - guaiDian_Index

    # 应用减速曲线
    if actual_decel_length > 0 and len(decel_vel_vector) > 0:
        vel_LuoLa_actual[guaiDian_Index:end_index] = decel_vel_vector[:actual_decel_length]

    # ★★★ 关键修正：确保减速后保持0速度，避免"先降后升" ★★★
    if end_index < len(vel_LuoLa_actual):
        vel_LuoLa_actual[end_index:] = 0  # 强制设为0速度
        print(f'修正提示：减速段结束后，剩余 {len(vel_LuoLa_actual) - end_index} 个时间点被设为0速度')

    # 使用cumsum重新计算实际位置曲线 (更高效)
    pos_LuoLa_actual = np.concatenate([[0], np.cumsum(vel_LuoLa_actual[:-1] * Ts)])

    # 确保终点精确
    if len(pos_LuoLa_actual) > 0:
        pos_LuoLa_actual[-1] = pos_LuoLa_final_target
    
    # ==================== 5. 可视化仿真结果 (最终优化版) ====================
    plt.figure(figsize=(12, 10))
    plt.suptitle('两段牵伸(最终优化版)运动曲线', fontsize=16, fontweight='bold')
    
    # 速度曲线对比
    plt.subplot(2, 1, 1)
    plt.plot(time_ZouChe, vel_ZouChe, 'b-', linewidth=2, label='走车速度')
    plt.plot(time_ZouChe, vel_LuoLa_actual, 'r--', linewidth=2, label='罗拉实际速度')
    plt.axvline(T_at_guaiDian, color='k', linestyle=':', linewidth=1.5, label='去同步拐点时刻')
    plt.title('速度曲线 (Velocity Profile)')
    plt.xlabel('时间 (s)')
    plt.ylabel('速度 (mm/s)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # 位置曲线对比
    plt.subplot(2, 1, 2)
    plt.plot(time_ZouChe, pos_ZouChe, 'b-', linewidth=2, label='走车位置')
    plt.plot(time_ZouChe, pos_LuoLa_actual, 'r--', linewidth=2, label='罗拉实际位置')
    plt.axhline(P_guaiDian_LuoLa, color='g', linestyle=':', linewidth=1.5, 
                label=f'去同步拐点位置: {P_guaiDian_LuoLa:.0f} mm')
    plt.title('位置曲线 (Position Profile)')
    plt.xlabel('时间 (s)')
    plt.ylabel('位置 (mm)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图片
    output_filename = 'simulation_curves_2Duan_Final_Optimized.png'
    plt.savefig(output_filename, dpi=300, bbox_inches='tight')
    print(f'\n最终优化版仿真图表已成功保存为: {output_filename}')
    
    # 显示图片
    plt.show()
    
    return time_ZouChe, pos_ZouChe, vel_ZouChe, pos_LuoLa_actual, vel_LuoLa_actual

if __name__ == "__main__":
    main()
