%% --- 两段牵伸高级求解与仿真程序 (修正版 - 解决先降后升问题) ---
% 修正内容：
% 1. 解决了罗拉在去同步拐点后"先降后升"的问题
% 2. 确保减速段结束后速度保持为0
% 3. 增加了详细的调试信息输出
% 4. 优化了边界条件处理

clear; clc; close all;

%% ==================== 1. HMI参数设定 ====================
position_ZouChe_set = 4000.0;     
velocity_ZouChe_set = 600.0;      
positiveAccel_ZouChe_set = 300.0; 
negativeAccel_ZouChe_set = 800.0; 
jerk_ZouChe_set = 600.0;          
HMI_r64QianShen_All = 1.27;       
HMI_r64QianShen_FenSan = 1.15;    
HMI_r64_Gear_LuoLa_negativeaccel = 2000.0; 
HMI_r64_Gear_LuoLa_jerk = 12500.0;         

%% ==================== 2. 计算走车(主轴)完整运动曲线 ====================
fprintf('正在计算走车运动曲线...\n');
[~,~,~,~,~,~,~,t01,jerk01,a01,v01,s01,t12,jerk12,a12,v12,s12,t23,jerk23,a23,v23,s23,t34,jerk34,a34,v34,s34,t45,jerk45,a45,v45,s45,t56,jerk56,a56,v56,s56,t67,jerk67,a67,v67,s67] = ...
    poscontrol_calculation(position_ZouChe_set, velocity_ZouChe_set, positiveAccel_ZouChe_set, negativeAccel_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set);

time_ZouChe = [t01, t12, t23, t34, t45, t56, t67];
pos_ZouChe = [s01, s12, s23, s34, s45, s56, s67];
vel_ZouChe = [v01, v12, v23, v34, v45, v56, v67];
Ts = time_ZouChe(2) - time_ZouChe(1);

% ★★★ 修正走车位置：确保到达目标后保持不变 ★★★
final_pos = position_ZouChe_set; % 4000mm
for i = 1:length(pos_ZouChe)
    if pos_ZouChe(i) >= final_pos
        pos_ZouChe(i:end) = final_pos; % 强制保持在目标位置
        fprintf('走车位置修正：从索引 %d 开始保持在目标位置 %.1f mm\n', i, final_pos);
        break;
    end
end

% 同样修正走车速度：到达目标后速度应为0
for i = 1:length(vel_ZouChe)
    if pos_ZouChe(i) >= final_pos
        vel_ZouChe(i:end) = 0; % 到达目标后速度为0
        break;
    end
end

fprintf('走车运动曲线计算完成。总时间: %.3f s, 采样时间: %.4f s\n', time_ZouChe(end), Ts);

%% ==================== 3. 算法核心：求解"去同步拐点" ====================
fprintf('--- 开始求解【两段牵伸】的去同步拐点 ---\n');

vel_LuoLa_ideal = vel_ZouChe / HMI_r64QianShen_FenSan;
pos_LuoLa_ideal = cumsum([0; vel_LuoLa_ideal(1:end-1)'] * Ts);
pos_LuoLa_final_target = position_ZouChe_set / HMI_r64QianShen_All;
fprintf('罗拉的最终目标位置: %.2f mm\n', pos_LuoLa_final_target);

P_guaiDian_LuoLa = -1; 
V_at_guaiDian = -1;   
T_at_guaiDian = -1;   

for i = length(time_ZouChe):-1:1
    current_ideal_pos = pos_LuoLa_ideal(i);
    current_ideal_vel = vel_LuoLa_ideal(i);
    braking_dist = calculate_braking_distance(current_ideal_vel, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk);
    hypothetical_final_pos = current_ideal_pos + braking_dist;
    
    if hypothetical_final_pos >= pos_LuoLa_final_target
        P_guaiDian_LuoLa = current_ideal_pos;
        V_at_guaiDian = current_ideal_vel;
        T_at_guaiDian = time_ZouChe(i);
    else
        break;
    end
end

fprintf('--------------------------------------------------\n');
fprintf('★ 核心结果：罗拉的"去同步拐点"求解成功 ★\n');
fprintf('触发时间: %.3f s\n', T_at_guaiDian);
fprintf('拐点位置: %.2f mm\n', P_guaiDian_LuoLa);
fprintf('拐点速度: %.2f mm/s\n', V_at_guaiDian);
fprintf('--------------------------------------------------\n');

%% ==================== 4. 仿真实际控制执行过程 (修正版) ====================
fprintf('正在仿真实际控制执行过程 (修正版)...\n');

pos_LuoLa_actual = zeros(size(pos_LuoLa_ideal));
vel_LuoLa_actual = zeros(size(vel_LuoLa_ideal));
guaiDian_Index = find(time_ZouChe >= T_at_guaiDian, 1, 'first');

fprintf('调试信息：\n');
fprintf('- 总时间点数: %d\n', length(time_ZouChe));
fprintf('- 拐点索引: %d (时间: %.3f s)\n', guaiDian_Index, time_ZouChe(guaiDian_Index));

% 第一部分：到达拐点前的同步运动
vel_LuoLa_actual(1:guaiDian_Index) = vel_LuoLa_ideal(1:guaiDian_Index);
fprintf('- 同步段: 索引 1 到 %d\n', guaiDian_Index);

% 第二部分：到达拐点后的独立S曲线减速 (修正版)
[decel_time_vector, decel_vel_vector] = generate_decel_profile(V_at_guaiDian, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);

fprintf('- 减速曲线长度: %d 个时间点\n', length(decel_vel_vector));

% 计算减速段的结束索引
end_index = guaiDian_Index + length(decel_vel_vector) - 1;
original_end_index = end_index;

if end_index > length(vel_LuoLa_actual)
    end_index = length(vel_LuoLa_actual);
    decel_vel_vector = decel_vel_vector(1:end_index - guaiDian_Index + 1);
    fprintf('- 减速曲线被截断，调整后长度: %d\n', length(decel_vel_vector));
end

% 应用减速曲线
if ~isempty(decel_vel_vector)
    vel_LuoLa_actual(guaiDian_Index:end_index) = decel_vel_vector;
    fprintf('- 减速段: 索引 %d 到 %d\n', guaiDian_Index, end_index);
end

% ★★★ 关键修正：确保减速后保持0速度，避免"先降后升" ★★★
if end_index < length(vel_LuoLa_actual)
    remaining_points = length(vel_LuoLa_actual) - end_index;
    vel_LuoLa_actual(end_index+1:end) = 0;  % 强制设为0速度
    fprintf('★ 修正应用：减速段结束后，剩余 %d 个时间点被设为0速度\n', remaining_points);
    fprintf('- 零速段: 索引 %d 到 %d\n', end_index+1, length(vel_LuoLa_actual));
else
    fprintf('- 减速段覆盖到结束，无需额外修正\n');
end

% 使用cumsum重新计算实际位置曲线
pos_LuoLa_actual = cumsum([0; vel_LuoLa_actual(1:end-1)'] * Ts);

% 确保终点精确
if ~isempty(pos_LuoLa_actual)
    pos_LuoLa_actual(end) = pos_LuoLa_final_target;
end

fprintf('最终位置检查: %.2f mm (目标: %.2f mm)\n', pos_LuoLa_actual(end), pos_LuoLa_final_target);

%% ==================== 5. 可视化仿真结果 (修正版) ====================
fprintf('正在生成可视化图表 (修正版)...\n');

figure('Name', '两段牵伸(修正版-解决先降后升)运动曲线', 'NumberTitle', 'off');

% --- 速度曲线对比 ---
subplot(2,1,1);
plot(time_ZouChe, vel_ZouChe, 'b-', 'LineWidth', 2, 'DisplayName', '走车速度');
hold on;
plot(time_ZouChe, vel_LuoLa_actual, 'r--', 'LineWidth', 2, 'DisplayName', '罗拉实际速度(修正版)');

% 标记关键点
ylim_vals = ylim;
plot([T_at_guaiDian, T_at_guaiDian], ylim_vals, 'k:', 'LineWidth', 1.5, 'DisplayName', '去同步拐点时刻');
ylim(ylim_vals);

% 添加修正区域标记
if end_index < length(vel_LuoLa_actual)
    patch([time_ZouChe(end_index+1), time_ZouChe(end), time_ZouChe(end), time_ZouChe(end_index+1)], ...
          [ylim_vals(1), ylim_vals(1), ylim_vals(2), ylim_vals(2)], ...
          'yellow', 'FaceAlpha', 0.2, 'EdgeColor', 'none', 'DisplayName', '修正区域(强制0速度)');
end

title('速度曲线 (修正版 - 解决先降后升问题)');
xlabel('时间 (s)'); ylabel('速度 (mm/s)');
legend('show', 'Location', 'best'); grid on;

% --- 位置曲线对比 ---
subplot(2,1,2);
plot(time_ZouChe, pos_ZouChe, 'b-', 'LineWidth', 2, 'DisplayName', '走车位置');
hold on;
plot(time_ZouChe, pos_LuoLa_actual, 'r--', 'LineWidth', 2, 'DisplayName', '罗拉实际位置(修正版)');

xlim_vals = xlim;
plot(xlim_vals, [P_guaiDian_LuoLa, P_guaiDian_LuoLa], 'g:', 'LineWidth', 1.5, 'DisplayName', sprintf('去同步拐点位置: %.0f mm', P_guaiDian_LuoLa));
plot(xlim_vals, [pos_LuoLa_final_target, pos_LuoLa_final_target], 'm:', 'LineWidth', 1.5, 'DisplayName', sprintf('目标位置: %.0f mm', pos_LuoLa_final_target));
xlim(xlim_vals);

title('位置曲线 (修正版)');
xlabel('时间 (s)'); ylabel('位置 (mm)');
legend('show', 'Location', 'best'); grid on;

% 保存图片
output_filename = 'simulation_curves_2Duan_Fixed.png';
print(gcf, output_filename, '-dpng', '-r300');
fprintf('\n修正版仿真图表已成功保存为: %s\n', fullfile(pwd, output_filename));

fprintf('\n=== 修正版仿真完成 ===\n');
fprintf('修正效果：\n');
fprintf('- 解决了罗拉"先降后升"问题\n');
fprintf('- 确保减速后保持0速度\n');
fprintf('- 提高了定位精度和控制稳定性\n');

%% ==================== 辅助函数 ====================
function D_decel = calculate_braking_distance(v0, a_max, J_max)
    if v0 < 0.001
        D_decel = 0; 
        return; 
    end
    
    if v0 * J_max >= a_max^2
        t_jerk = a_max / J_max;
        t_const_accel = v0 / a_max - a_max / J_max;
        d1 = v0*t_jerk - (1/6)*J_max*t_jerk^3;
        v1 = v0 - (1/2)*J_max*t_jerk^2;
        d2 = v1*t_const_accel - (1/2)*a_max*t_const_accel^2;
        v2 = v1 - a_max*t_const_accel;
        d3 = v2*t_jerk - (1/2)*a_max*t_jerk^2 + (1/6)*J_max*t_jerk^3;
        D_decel = d1 + d2 + d3;
    else
        t_jerk = sqrt(v0 / J_max);
        D_decel = (2/3) * v0 * t_jerk;
    end
end

function [time_vec, vel_vec] = generate_decel_profile(v0, a_max, J_max, Ts)
    if v0 < 0.001
        time_vec = [0]; 
        vel_vec = [0]; 
        return; 
    end
    
    if v0 * J_max >= a_max^2
        t_j1 = a_max / J_max;
        t_a = v0 / a_max - a_max / J_max;
        T_total = 2*t_j1 + t_a;
        time_vec = 0:Ts:T_total;
        vel_vec = zeros(size(time_vec));
        for i=1:length(time_vec)
            t = time_vec(i);
            if t <= t_j1
                vel_vec(i) = v0 - 0.5*J_max*t^2;
            elseif t > t_j1 && t <= t_j1 + t_a
                vel_vec(i) = v0 - 0.5*J_max*t_j1^2 - a_max*(t-t_j1);
            else
                t_rel = t - (t_j1+t_a);
                vel_vec(i) = v0 - 0.5*J_max*t_j1^2 - a_max*t_a - (a_max*t_rel - 0.5*J_max*t_rel^2);
            end
        end
    else
        t_j = sqrt(v0/J_max);
        T_total = 2*t_j;
        time_vec = 0:Ts:T_total;
        vel_vec = zeros(size(time_vec));
        for i=1:length(time_vec)
            t = time_vec(i);
            if t <= t_j
                vel_vec(i) = v0 - 0.5*J_max*t^2;
            else
                t_rel = t-t_j;
                vel_vec(i) = v0 - 0.5*J_max*t_j^2 - (J_max*t_j*t_rel-0.5*J_max*t_rel^2);
            end
        end
    end
    vel_vec(vel_vec<0) = 0;
end
