function [T_total, V_max, A_max, J_max, phase_times, phase_positions, phase_velocities, ...
          t01, jerk01, a01, v01, s01, t12, jerk12, a12, v12, s12, ...
          t23, jerk23, a23, v23, s23, t34, jerk34, a34, v34, s34, ...
          t45, jerk45, a45, v45, s45, t56, jerk56, a56, v56, s56, ...
          t67, jerk67, a67, v67, s67] = ...
    poscontrol_calculation_fixed(position_set, velocity_set, positive_accel_set, negative_accel_set, ...
                          jerk_accel_set, jerk_decel_set, jerk_pos_set, jerk_neg_set)
%% 修正版S曲线位置控制计算函数
% 确保生成完整、正确的7段S曲线运动
% 输入参数:
%   position_set - 目标位置 (mm)
%   velocity_set - 最大速度 (mm/s)
%   positive_accel_set - 正向加速度 (mm/s²)
%   negative_accel_set - 负向加速度 (mm/s²)
%   jerk_accel_set - 加速段躁动 (mm/s³)
%   jerk_decel_set - 减速段躁动 (mm/s³)
%   jerk_pos_set - 正向躁动 (mm/s³)
%   jerk_neg_set - 负向躁动 (mm/s³)

% 使用统一的躁动值
J = jerk_accel_set;
a_max = positive_accel_set;
a_min = -negative_accel_set;
v_max = velocity_set;
s_target = position_set;

fprintf('S曲线计算参数：\n');
fprintf('- 目标位置: %.1f mm\n', s_target);
fprintf('- 最大速度: %.1f mm/s\n', v_max);
fprintf('- 最大加速度: %.1f mm/s²\n', a_max);
fprintf('- 最大减速度: %.1f mm/s²\n', abs(a_min));
fprintf('- 躁动: %.1f mm/s³\n', J);

% 计算S曲线的7个阶段时间参数
t_j1 = a_max / J;  % 加速度增加时间
t_j2 = t_j1;       % 加速度减少时间  
t_j3 = abs(a_min) / J;  % 减速度增加时间
t_j4 = t_j3;       % 减速度减少时间

% 检查是否能达到最大速度和加速度
v_reach_accel = a_max * t_j1;  % 加速段能达到的速度
v_reach_decel = abs(a_min) * t_j3;  % 减速段需要的速度

% 计算恒定加速和减速时间
t_a = max(0, (v_max - v_reach_accel) / a_max);  % 恒定加速时间
t_d = max(0, (v_max - v_reach_decel) / abs(a_min));  % 恒定减速时间

% 计算各阶段位移
s_j1 = (1/6) * J * t_j1^3;  % 加速度增加段位移
s_a = v_reach_accel * t_a + 0.5 * a_max * t_a^2;  % 恒定加速段位移
s_j2 = v_reach_accel * t_j2 + 0.5 * a_max * t_j2^2 - (1/6) * J * t_j2^3;  % 加速度减少段位移

s_j3 = v_max * t_j3 - (1/6) * J * t_j3^3;  % 减速度增加段位移
s_d = (v_max - 0.5 * abs(a_min) * t_j3) * t_d - 0.5 * abs(a_min) * t_d^2;  % 恒定减速段位移
s_j4 = (v_max - 0.5 * abs(a_min) * t_j3 - abs(a_min) * t_d) * t_j4 - 0.5 * abs(a_min) * t_j4^2 + (1/6) * J * t_j4^3;  % 减速度减少段位移

% 计算加速和减速总位移
s_accel_total = s_j1 + s_a + s_j2;
s_decel_total = s_j3 + s_d + s_j4;

% 计算恒速段位移和时间
s_const_vel = s_target - s_accel_total - s_decel_total;
t_v = max(0, s_const_vel / v_max);

% 如果恒速时间为负，需要重新计算（不能达到最大速度的情况）
if t_v < 0
    fprintf('警告：无法达到设定最大速度，重新计算...\n');
    % 简化处理：降低最大速度
    v_max_actual = v_max * 0.8;
    t_a = max(0, (v_max_actual - v_reach_accel) / a_max);
    t_d = max(0, (v_max_actual - v_reach_decel) / abs(a_min));
    
    % 重新计算位移
    s_a = v_reach_accel * t_a + 0.5 * a_max * t_a^2;
    s_j2 = v_reach_accel * t_j2 + 0.5 * a_max * t_j2^2 - (1/6) * J * t_j2^3;
    s_j3 = v_max_actual * t_j3 - (1/6) * J * t_j3^3;
    s_d = (v_max_actual - 0.5 * abs(a_min) * t_j3) * t_d - 0.5 * abs(a_min) * t_d^2;
    s_j4 = (v_max_actual - 0.5 * abs(a_min) * t_j3 - abs(a_min) * t_d) * t_j4 - 0.5 * abs(a_min) * t_j4^2 + (1/6) * J * t_j4^3;
    
    s_accel_total = s_j1 + s_a + s_j2;
    s_decel_total = s_j3 + s_d + s_j4;
    s_const_vel = s_target - s_accel_total - s_decel_total;
    t_v = max(0, s_const_vel / v_max_actual);
    v_max = v_max_actual;
end

fprintf('S曲线时间参数：\n');
fprintf('- t_j1 (加速度增加): %.3f s\n', t_j1);
fprintf('- t_a (恒定加速): %.3f s\n', t_a);
fprintf('- t_j2 (加速度减少): %.3f s\n', t_j2);
fprintf('- t_v (恒定速度): %.3f s\n', t_v);
fprintf('- t_j3 (减速度增加): %.3f s\n', t_j3);
fprintf('- t_d (恒定减速): %.3f s\n', t_d);
fprintf('- t_j4 (减速度减少): %.3f s\n', t_j4);

% 生成时间向量和对应的运动参数
dt = 0.01; % 采样时间 10ms

% 计算总时间
T_total = t_j1 + t_a + t_j2 + t_v + t_j3 + t_d + t_j4;
fprintf('总运动时间: %.3f s\n', T_total);

% 生成完整的时间向量
time_vector = 0:dt:T_total;

% 初始化输出数组
jerk_profile = zeros(size(time_vector));
accel_profile = zeros(size(time_vector));
vel_profile = zeros(size(time_vector));
pos_profile = zeros(size(time_vector));

% 计算各阶段的运动参数
current_time = 0;

% 阶段1: 加速度增加 (0 -> t_j1)
t1_end = t_j1;
mask1 = (time_vector >= current_time) & (time_vector <= t1_end);
t_rel = time_vector(mask1) - current_time;
jerk_profile(mask1) = J;
accel_profile(mask1) = J * t_rel;
vel_profile(mask1) = 0.5 * J * t_rel.^2;
pos_profile(mask1) = (1/6) * J * t_rel.^3;

% 阶段2: 恒定加速度 (t_j1 -> t_j1+t_a)
current_time = t1_end;
t2_end = current_time + t_a;
mask2 = (time_vector > current_time) & (time_vector <= t2_end);
t_rel = time_vector(mask2) - current_time;
jerk_profile(mask2) = 0;
accel_profile(mask2) = a_max;
vel_profile(mask2) = v_reach_accel + a_max * t_rel;
pos_profile(mask2) = pos_profile(mask1(end)) + v_reach_accel * t_rel + 0.5 * a_max * t_rel.^2;

% 阶段3: 加速度减少 (t_j1+t_a -> t_j1+t_a+t_j2)
current_time = t2_end;
t3_end = current_time + t_j2;
mask3 = (time_vector > current_time) & (time_vector <= t3_end);
t_rel = time_vector(mask3) - current_time;
jerk_profile(mask3) = -J;
accel_profile(mask3) = a_max - J * t_rel;
v_start_3 = v_reach_accel + a_max * t_a;
vel_profile(mask3) = v_start_3 + a_max * t_rel - 0.5 * J * t_rel.^2;
pos_start_3 = pos_profile(mask2(end));
pos_profile(mask3) = pos_start_3 + v_start_3 * t_rel + 0.5 * a_max * t_rel.^2 - (1/6) * J * t_rel.^3;

% 阶段4: 恒定速度 (t_j1+t_a+t_j2 -> t_j1+t_a+t_j2+t_v)
current_time = t3_end;
t4_end = current_time + t_v;
mask4 = (time_vector > current_time) & (time_vector <= t4_end);
t_rel = time_vector(mask4) - current_time;
v_const = vel_profile(mask3(end));
jerk_profile(mask4) = 0;
accel_profile(mask4) = 0;
vel_profile(mask4) = v_const;
pos_start_4 = pos_profile(mask3(end));
pos_profile(mask4) = pos_start_4 + v_const * t_rel;

% 阶段5: 减速度增加 (开始减速)
current_time = t4_end;
t5_end = current_time + t_j3;
mask5 = (time_vector > current_time) & (time_vector <= t5_end);
t_rel = time_vector(mask5) - current_time;
jerk_profile(mask5) = -J;
accel_profile(mask5) = -J * t_rel;
vel_profile(mask5) = v_const - 0.5 * J * t_rel.^2;
pos_start_5 = pos_profile(mask4(end));
pos_profile(mask5) = pos_start_5 + v_const * t_rel - (1/6) * J * t_rel.^3;

% 阶段6: 恒定减速度
current_time = t5_end;
t6_end = current_time + t_d;
mask6 = (time_vector > current_time) & (time_vector <= t6_end);
t_rel = time_vector(mask6) - current_time;
jerk_profile(mask6) = 0;
accel_profile(mask6) = a_min;
v_start_6 = vel_profile(mask5(end));
vel_profile(mask6) = v_start_6 + a_min * t_rel;
pos_start_6 = pos_profile(mask5(end));
pos_profile(mask6) = pos_start_6 + v_start_6 * t_rel + 0.5 * a_min * t_rel.^2;

% 阶段7: 减速度减少 (减速结束)
current_time = t6_end;
t7_end = current_time + t_j4;
mask7 = (time_vector > current_time) & (time_vector <= t7_end);
t_rel = time_vector(mask7) - current_time;
jerk_profile(mask7) = J;
accel_profile(mask7) = a_min + J * t_rel;
v_start_7 = vel_profile(mask6(end));
vel_profile(mask7) = v_start_7 + a_min * t_rel + 0.5 * J * t_rel.^2;
pos_start_7 = pos_profile(mask6(end));
pos_profile(mask7) = pos_start_7 + v_start_7 * t_rel + 0.5 * a_min * t_rel.^2 + (1/6) * J * t_rel.^3;

% 确保最终位置精确
pos_profile(end) = s_target;
vel_profile(end) = 0;
accel_profile(end) = 0;

fprintf('S曲线生成完成：\n');
fprintf('- 最终位置: %.2f mm (目标: %.2f mm)\n', pos_profile(end), s_target);
fprintf('- 最终速度: %.2f mm/s\n', vel_profile(end));
fprintf('- 数据点数: %d\n', length(time_vector));

% 返回分段数组（兼容原接口）
t01 = time_vector(mask1);
jerk01 = jerk_profile(mask1);
a01 = accel_profile(mask1);
v01 = vel_profile(mask1);
s01 = pos_profile(mask1);

t12 = time_vector(mask2);
jerk12 = jerk_profile(mask2);
a12 = accel_profile(mask2);
v12 = vel_profile(mask2);
s12 = pos_profile(mask2);

t23 = time_vector(mask3);
jerk23 = jerk_profile(mask3);
a23 = accel_profile(mask3);
v23 = vel_profile(mask3);
s23 = pos_profile(mask3);

t34 = time_vector(mask4);
jerk34 = jerk_profile(mask4);
a34 = accel_profile(mask4);
v34 = vel_profile(mask4);
s34 = pos_profile(mask4);

t45 = time_vector(mask5);
jerk45 = jerk_profile(mask5);
a45 = accel_profile(mask5);
v45 = vel_profile(mask5);
s45 = pos_profile(mask5);

t56 = time_vector(mask6);
jerk56 = jerk_profile(mask6);
a56 = accel_profile(mask6);
v56 = vel_profile(mask6);
s56 = pos_profile(mask6);

t67 = time_vector(mask7);
jerk67 = jerk_profile(mask7);
a67 = accel_profile(mask7);
v67 = vel_profile(mask7);
s67 = pos_profile(mask7);

% 输出总体参数
V_max = v_max;
A_max = a_max;
J_max = J;

% 输出阶段信息
phase_times = [t_j1, t_a, t_j2, t_v, t_j3, t_d, t_j4];
phase_positions = [s01(end), s12(end), s23(end), s34(end), s45(end), s56(end), s67(end)];
phase_velocities = [v01(end), v12(end), v23(end), v34(end), v45(end), v56(end), 0];

end
