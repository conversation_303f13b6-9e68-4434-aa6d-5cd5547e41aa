%% --- 两段牵伸高级求解与仿真程序 (全轨迹反算算法) ---
% 功能：
% 1. 采用“全轨迹反算”算法，精确求解“去同步拐点”。
% 2. 保证算法能正确处理拐点落在走车“匀速段”或“减速段”的各种情况。
% 3. 仿真并可视化最终的控制执行过程。
% 4. 将仿真图表保存为图片文件。

clear; clc; close all;

%% ==================== 1. HMI参数设定 ====================
% 走车 (Master Axis)
position_ZouChe_set = 4000.0;     
velocity_ZouChe_set = 600.0;      
positiveAccel_ZouChe_set = 300.0; 
negativeAccel_ZouChe_set = 800.0; 
jerk_ZouChe_set = 600.0;          

% 牵伸控制 (Tension Control)
g_i16_QianShen_Select = 2;        % 两段牵伸
HMI_r64QianShen_All = 1.27;       
HMI_r64QianShen_FenSan = 1.15;    

% 罗拉 (Slave Axis) - 独立减速定位参数
HMI_r64_Gear_LuoLa_negativeaccel = 2000.0; 
HMI_r64_Gear_LuoLa_jerk = 12500.0;         

%% ==================== 2. 计算走车(主轴)完整运动曲线 ====================
% 调用您提供的函数
[~,~,~,~,~,~,~,t01,jerk01,a01,v01,s01,t12,jerk12,a12,v12,s12,t23,jerk23,a23,v23,s23,t34,jerk34,a34,v34,s34,t45,jerk45,a45,v45,s45,t56,jerk56,a56,v56,s56,t67,jerk67,a67,v67,s67] = ...
    poscontrol_calculation(position_ZouChe_set, velocity_ZouChe_set, positiveAccel_ZouChe_set, negativeAccel_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set, jerk_ZouChe_set);

% 整合为完整数组
time_ZouChe = [t01, t12, t23, t34, t45, t56, t67];
pos_ZouChe = [s01, s12, s23, s34, s45, s56, s67];
vel_ZouChe = [v01, v12, v23, v34, v45, v56, s67(end)./t67(end)];
Ts = time_ZouChe(2) - time_ZouChe(1); % 获取采样时间

%% ==================== 3. 算法核心：求解“去同步拐点” ====================
fprintf('--- 开始求解【两段牵伸】的去同步拐点 ---\n');

% 3.1. 仿真一条“理想同步”下的罗拉轨迹
% 这条轨迹假设罗拉全程都与走车按牵伸比同步（包括减速段）
vel_LuoLa_ideal = vel_ZouChe / HMI_r64QianShen_FenSan;
pos_LuoLa_ideal = zeros(size(vel_LuoLa_ideal));
for i = 2:length(time_ZouChe)
    pos_LuoLa_ideal(i) = pos_LuoLa_ideal(i-1) + vel_LuoLa_ideal(i-1) * Ts;
end

% 3.2. 定义罗拉最终需要到达的目标位置
pos_LuoLa_final_target = position_ZouChe_set / HMI_r64QianShen_All;
fprintf('罗拉的最终目标位置: %.2f mm\n', pos_LuoLa_final_target);

% 3.3. “全轨迹反算”求解拐点
P_guaiDian_LuoLa = -1; % 初始化拐点位置
V_at_guaiDian = -1;   % 初始化拐点速度
T_at_guaiDian = -1;   % 初始化拐点时间

% 从理想轨迹的终点开始，向前反向遍历
for i = length(time_ZouChe):-1:1
    % 获取当前点的理想速度和位置
    current_ideal_pos = pos_LuoLa_ideal(i);
    current_ideal_vel = vel_LuoLa_ideal(i);
    
    % “试算”：如果从这个点开始独立减速，最终会停在哪里？
    braking_dist = calculate_braking_distance(current_ideal_vel, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk);
    hypothetical_final_pos = current_ideal_pos + braking_dist;
    
    % 判断试算结果是否满足要求
    % 我们要找的是最后一个使得“期望停止位置 >= 最终目标位置”的点
    if hypothetical_final_pos >= pos_LuoLa_final_target
        % 持续更新，直到循环结束，最后一次更新的值就是我们要找的拐点
        P_guaiDian_LuoLa = current_ideal_pos;
        V_at_guaiDian = current_ideal_vel;
        T_at_guaiDian = time_ZouChe(i);
    else
        % 一旦不满足条件，说明上一个点就是我们要找的精确拐点，跳出循环
        break;
    end
end

fprintf('--------------------------------------------------\n');
fprintf('★ 核心结果：罗拉的“去同步拐点”求解成功 ★\n');
fprintf('触发时间: %.3f s\n', T_at_guaiDian);
fprintf('拐点位置: %.2f mm\n', P_guaiDian_LuoLa);
fprintf('拐点速度: %.2f mm/s\n', V_at_guaiDian);
fprintf('--------------------------------------------------\n');


%% ==================== 4. 仿真实际控制执行过程 ====================
pos_LuoLa_actual = zeros(size(pos_LuoLa_ideal));
vel_LuoLa_actual = zeros(size(vel_LuoLa_ideal));
guaiDian_Index = find(time_ZouChe >= T_at_guaiDian, 1, 'first');

% 第一部分：到达拐点前的同步运动
vel_LuoLa_actual(1:guaiDian_Index) = vel_LuoLa_ideal(1:guaiDian_Index);

% 第二部分：到达拐点后的独立S曲线减速
% 为了可视化，我们在这里生成减速段的曲线
[decel_time_vector, decel_vel_vector] = generate_decel_profile(V_at_guaiDian, HMI_r64_Gear_LuoLa_negativeaccel, HMI_r64_Gear_LuoLa_jerk, Ts);
end_index = guaiDian_Index + length(decel_vel_vector) -1;
if end_index > length(vel_LuoLa_actual)
    end_index = length(vel_LuoLa_actual);
    decel_vel_vector = decel_vel_vector(1:end_index - guaiDian_Index + 1);
end
vel_LuoLa_actual(guaiDian_Index:end_index) = decel_vel_vector;


% 整合出最终的实际位置曲线
for i = 2:length(time_ZouChe)
    pos_LuoLa_actual(i) = pos_LuoLa_actual(i-1) + vel_LuoLa_actual(i-1) * Ts;
end
pos_LuoLa_actual(end) = pos_LuoLa_final_target; % 确保终点精确


%% ==================== 5. 可视化仿真结果 ====================
figure('Name', '两段牵伸(高级算法)运动曲线', 'NumberTitle', 'off');
% ... (绘图和保存代码与之前类似)
% --- 速度曲线对比 ---
subplot(2,1,1);
plot(time_ZouChe, vel_ZouChe, 'b-', 'LineWidth', 2, 'DisplayName', '走车速度');
hold on;
plot(time_ZouChe, vel_LuoLa_actual, 'r--', 'LineWidth', 2, 'DisplayName', '罗拉实际速度');
xline(T_at_guaiDian, 'k:', 'LineWidth', 1.5, 'DisplayName', '去同步拐点时刻');
title('速度曲线 (Velocity Profile)');
xlabel('时间 (s)'); ylabel('速度 (mm/s)');
legend('show', 'Location', 'best'); grid on;

% --- 位置曲线对比 ---
subplot(2,1,2);
plot(time_ZouChe, pos_ZouChe, 'b-', 'LineWidth', 2, 'DisplayName', '走车位置');
hold on;
plot(time_ZouChe, pos_LuoLa_actual, 'r--', 'LineWidth', 2, 'DisplayName', '罗拉实际位置');
yline(P_guaiDian_LuoLa, 'g:', 'LineWidth', 1.5, 'DisplayName', sprintf('去同步拐点位置: %.0f mm', P_guaiDian_LuoLa));
title('位置曲线 (Position Profile)');
xlabel('时间 (s)'); ylabel('位置 (mm)');
legend('show', 'Location', 'best'); grid on;

% --- 保存图片 ---
output_filename = 'simulation_curves_2Duan_Advanced.png';
print(gcf, output_filename, '-dpng', '-r300');
fprintf('\n高级算法仿真图表已成功保存为: %s\n', fullfile(pwd, output_filename));


%% ==================== 辅助函数 ====================
function D_decel = calculate_braking_distance(v0, a_max, J_max)
    if v0 < 0.001; D_decel = 0; return; end % 处理速度为0的情况
    if v0 * J_max >= a_max^2
        t_jerk = a_max / J_max;
        t_const_accel = v0 / a_max - a_max / J_max;
        d1 = v0*t_jerk - (1/6)*J_max*t_jerk^3;
        v1 = v0 - (1/2)*J_max*t_jerk^2;
        d2 = v1*t_const_accel - (1/2)*a_max*t_const_accel^2;
        v2 = v1 - a_max*t_const_accel;
        d3 = v2*t_jerk - (1/2)*a_max*t_jerk^2 + (1/6)*J_max*t_jerk^3;
        D_decel = d1 + d2 + d3;
    else
        t_jerk = sqrt(v0 / J_max);
        D_decel = v0 * t_jerk - (1/3)*J_max*t_jerk^3; % 修正了三角形减速的距离公式
    end
end

function [time_vec, vel_vec] = generate_decel_profile(v0, a_max, J_max, Ts)
    if v0 < 0.001; time_vec=[0]; vel_vec=[0]; return; end
    if v0 * J_max >= a_max^2 % 梯形
        t_j1 = a_max / J_max;
        t_a = v0 / a_max - a_max / J_max;
        T_total = 2*t_j1 + t_a;
        time_vec = 0:Ts:T_total;
        vel_vec = zeros(size(time_vec));
        for i=1:length(time_vec)
            t = time_vec(i);
            if t <= t_j1
                vel_vec(i) = v0 - 0.5*J_max*t^2;
            elseif t > t_j1 && t <= t_j1 + t_a
                vel_vec(i) = v0 - 0.5*J_max*t_j1^2 - a_max*(t-t_j1);
            else
                t_rel = t - (t_j1+t_a);
                vel_vec(i) = v0 - 0.5*J_max*t_j1^2 - a_max*t_a - (a_max*t_rel - 0.5*J_max*t_rel^2);
            end
        end
    else % 三角形
        t_j = sqrt(v0/J_max);
        T_total = 2*t_j;
        time_vec = 0:Ts:T_total;
        vel_vec = zeros(size(time_vec));
         for i=1:length(time_vec)
            t = time_vec(i);
            if t <= t_j
                 vel_vec(i) = v0 - 0.5*J_max*t^2;
            else
                t_rel = t-t_j;
                vel_vec(i) = v0 - 0.5*J_max*t_j^2 - (J_max*t_j*t_rel-0.5*J_max*t_rel^2);
            end
         end
    end
    vel_vec(vel_vec<0) = 0; % 保证速度不为负
end